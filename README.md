# Tucsenberg Web - Next.js 15 项目

这是一个基于 [Next.js 15](https://nextjs.org) 的企业级 Web 应用项目，采用最新的开发技术栈和严格的代码质量标准。

## 🚀 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript 5
- **样式**: Tailwind CSS 4 + shadcn/ui
- **代码质量**: ESLint 9 + Prettier + Lefthook
- **包管理**: pnpm

## 📋 代码质量标准

本项目采用企业级零警告政策，所有代码质量检查都设为 `error` 级别：

### Import 排序配置

项目使用 `@trivago/prettier-plugin-sort-imports` 自动排序 import 语句，按以下顺序：

1. **Next.js imports**: `next/*`
2. **React imports**: `react/*`
3. **第三方库**: 按字母顺序排列
4. **内部模块**: `@/*` 别名导入
5. **相对路径**: `./` 和 `../`

#### 配置文件位置

- **Prettier 配置**: `.prettierrc.json`
- **ESLint 配置**: `eslint.config.mjs`
- **VSCode 设置**: `.vscode/settings.json`

#### Import 排序示例

```typescript
// ✅ 正确的 import 顺序
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { clsx } from 'clsx';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import './styles.css';
```

## 🛠️ 开发工作流

### 快速开始

1. **安装依赖**

```bash
pnpm install
```

2. **启动开发服务器**

```bash
pnpm dev
```

3. **打开浏览器访问** [http://localhost:3000](http://localhost:3000)

### 代码质量命令

```bash
# 格式化代码并修复 ESLint 问题
pnpm format:lint

# 仅检查代码格式
pnpm format:check

# 仅修复代码格式
pnpm format:fix

# 检查 ESLint 规则
pnpm lint:check

# 修复 ESLint 问题
pnpm lint:fix

# TypeScript 类型检查
pnpm type-check

# 完整质量检查
pnpm quality:check

# 构建项目
pnpm build
```

### Git 工作流

项目配置了 Lefthook Git 钩子，在提交时自动执行代码质量检查：

- **pre-commit**: TypeScript 检查、ESLint 检查、Prettier 格式检查
- **commit-msg**: 提交信息格式验证
- **pre-push**: 构建测试和最终质量检查

### VSCode 配置

项目已配置 VSCode 设置（`.vscode/settings.json`）：

- **保存时自动格式化**: `formatOnSave: true`
- **保存时修复 ESLint**: `source.fixAll.eslint: "explicit"`
- **禁用内置 import 排序**: `source.organizeImports: "never"`
- **使用 Prettier 格式化器**: 所有支持的文件类型

## 🔧 故障排除

### Import 排序问题

**问题**: Import 语句没有按预期排序
**解决方案**:

1. 确认 `.prettierrc.json` 配置正确
2. 运行 `pnpm format:fix` 手动格式化
3. 检查 VSCode 是否安装了 Prettier 扩展
4. 确认 `source.organizeImports: "never"` 设置生效

**问题**: ESLint 和 Prettier 冲突
**解决方案**:

1. 确认 `eslint-config-prettier` 在 ESLint 配置的最后
2. 检查是否有冲突的 import 相关规则
3. 运行 `pnpm format:lint` 统一处理

**问题**: Git 钩子失败
**解决方案**:

1. 运行 `pnpm lefthook install` 重新安装钩子
2. 确保所有质量检查通过: `pnpm quality:check`
3. 检查提交信息格式是否符合规范

### 常见错误

**TypeScript 错误**: 运行 `pnpm type-check` 检查类型问题
**ESLint 错误**: 运行 `pnpm lint:fix` 自动修复
**格式化错误**: 运行 `pnpm format:fix` 自动格式化

## 📚 学习资源

### Next.js 相关

- [Next.js 15 Documentation](https://nextjs.org/docs) - 官方文档
- [Next.js Learn](https://nextjs.org/learn) - 交互式教程
- [Next.js GitHub](https://github.com/vercel/next.js) - 源码仓库

### 代码质量工具

- [ESLint 配置指南](https://eslint.org/docs/user-guide/configuring/)
- [Prettier 配置选项](https://prettier.io/docs/en/configuration.html)
- [@trivago/prettier-plugin-sort-imports](https://github.com/trivago/prettier-plugin-sort-imports)
- [Lefthook Git 钩子](https://github.com/evilmartians/lefthook)

## 🚀 部署

### Vercel 部署（推荐）

1. 推送代码到 GitHub 仓库
2. 在 [Vercel](https://vercel.com/new) 导入项目
3. Vercel 会自动检测 Next.js 项目并配置构建设置
4. 部署完成后获得生产环境 URL

### 部署前检查

确保所有质量检查通过：

```bash
pnpm quality:check
pnpm build
```

### 环境变量

在部署平台配置必要的环境变量（如果有）。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'feat: add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

### 提交信息格式

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>(<scope>): <subject>

类型 (type):
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式化
- refactor: 代码重构
- test: 测试相关
- chore: 构建工具或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
