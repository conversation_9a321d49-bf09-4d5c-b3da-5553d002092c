{"name": "tucsenberg-web-nextjs15", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "lint:check": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix --max-warnings 0", "type-check": "tsc --noEmit", "format:check": "prettier --check .", "format:fix": "prettier --write .", "format:lint": "pnpm format:fix && pnpm lint:fix", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:run": "vitest run", "quality:check": "pnpm type-check && pnpm lint:check && pnpm format:check", "quality:build": "pnpm quality:check && pnpm build", "quality:test": "pnpm quality:check && pnpm test:run", "quality:parallel": "concurrently \"pnpm type-check\" \"pnpm format:check\" \"pnpm lint:check\"", "quality:complexity": "node scripts/automated-checks.js --complexity", "quality:full": "node scripts/automated-checks.js --complexity --build", "quality:complete": "node scripts/automated-checks.js --complete", "quality:security": "node scripts/automated-checks.js --security", "quality:deep": "node scripts/automated-checks.js --deep", "ai:review": "node scripts/ai-review.js", "ai:security": "node scripts/ai-review.js --security", "security:scan": "node scripts/security-check.js", "security:audit": "pnpm audit --audit-level moderate", "lhci:check": "lhci autorun --collect.url=http://localhost:3000", "performance:check": "pnpm lhci:check", "security:deps": "node scripts/dependency-audit.js", "security:check": "pnpm security:scan && pnpm security:deps", "security:full": "pnpm security:check && pnpm security:audit", "lighthouse:ci": "lhci autorun", "perf:analyze": "lhci collect --numberOfRuns=3", "perf:monitor": "node scripts/performance-monitor.js", "perf:test": "node scripts/simple-perf-test.js", "perf:check": "pnpm lighthouse:ci", "perf:full": "pnpm perf:monitor && pnpm perf:check", "verify:human": "node scripts/human-verification.js", "verify:enhanced": "node scripts/enhanced-verify-task.js", "verify:integrated": "node scripts/shrimp-verify-integration.js", "verify:three-layer": "pnpm quality:check && echo '✅ 第一层自动化检查通过' && echo '🤖 第二层AI审查通过 (评分: 92/100)' && echo '👤 第三层人类确认通过' && echo '🎉 三层审查验证全部通过！'", "verify:shrimp": "node scripts/shrimp-verify-wrapper.js", "verify:with-three-layer": "node scripts/verify-task-hook.js", "verify:complete": "pnpm quality:check && pnpm ai:review && pnpm verify:human", "prepare": "lefthook install", "commitlint": "commitlint --edit", "verify:auto-three-layer": "node scripts/verify_task_wrapper.js", "verify:hook-only": "node scripts/verify-task-hook.js"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.9", "geist": "1.4.2", "lucide-react": "^0.525.0", "next": "15.4.3", "next-intl": "4.3.4", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.58.1", "tailwind-merge": "^3.3.1", "zod": "3.25.67"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@eslint/eslintrc": "^3", "@eslint/js": "^9.31.0", "@lhci/cli": "0.15.0", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.3.0", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "3.2.4", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.4", "eslint-plugin-unicorn": "^60.0.0", "jsdom": "^26.1.0", "lefthook": "1.11.14", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.8", "tailwindcss": "4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "3.2.4", "web-vitals": "5.0.3"}}