import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

/**
 * next-intl 4.3.4 请求配置
 * 动态加载翻译消息文件
 */
export default getRequestConfig(async ({ requestLocale }) => {
  // 验证传入的语言是否受支持
  let locale = await requestLocale;

  // 确保语言在支持的列表中
  if (locale == null || !routing.locales.includes(locale as 'en' | 'zh')) {
    locale = routing.defaultLocale;
  }

  // 安全的白名单验证
  const allowedLocales = ['en', 'zh'] as const;
  if (!allowedLocales.includes(locale as (typeof allowedLocales)[number])) {
    throw new Error(`Unsupported locale: ${locale}`);
  }

  // 使用静态导入映射，避免动态路径构建
  const messageModules = {
    en: () => import('../../messages/en.json'),
    zh: () => import('../../messages/zh.json'),
  } as const;

  try {
    const messages = (await messageModules[
      locale as keyof typeof messageModules
    ]()) as {
      default: Record<string, unknown>;
    };

    return {
      locale,
      messages: messages.default,
    };
  } catch {
    // 如果翻译文件不存在，使用默认语言（安全的静态导入）
    const defaultMessages = await messageModules[routing.defaultLocale]();

    return {
      locale: routing.defaultLocale,
      messages: defaultMessages.default,
    };
  }
});
