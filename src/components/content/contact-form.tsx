'use client';

import { type ReactElement } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { type ContactFormData, contactFormSchema } from '@/lib/validations';

/**
 * 联系表单数据接口
 */
interface ContactFormData {
  name: string;
  email: string;
  purpose?: string;
  region?: string;
  message?: string;
}

/**
 * 表单字段组件属性
 */
interface FormFieldProps {
  formData: ContactFormData;
  onInputChange: (field: keyof ContactFormData, value: string) => void;
  t: (key: string) => string;
  validationErrors: { name?: string; email?: string };
}

/**
 * 简单的邮箱验证正则表达式
 * 符合基本的邮箱格式要求：用户名@域名.后缀
 */
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * 表单字段组件
 */
function FormFields({
  formData,
  onInputChange,
  t,
  validationErrors,
}: FormFieldProps): ReactElement {
  return (
    <>
      {/* 必填项：姓名 */}
      <div className='space-y-2'>
        <Label htmlFor='name' className='text-sm font-medium'>
          {t('fields.name.label')} <span className='text-red-500'>*</span>
        </Label>
        <Input
          id='name'
          type='text'
          value={formData.name}
          onChange={(e) => onInputChange('name', e.target.value)}
          placeholder={t('fields.name.placeholder')}
          required
          aria-required='true'
          className={`w-full ${validationErrors.name ? 'border-red-500' : ''}`}
        />
        {validationErrors.name && (
          <p className='text-sm text-red-600 dark:text-red-400'>
            {validationErrors.name}
          </p>
        )}
      </div>

      {/* 必填项：邮箱 */}
      <div className='space-y-2'>
        <Label htmlFor='email' className='text-sm font-medium'>
          {t('fields.email.label')} <span className='text-red-500'>*</span>
        </Label>
        <Input
          id='email'
          type='email'
          value={formData.email}
          onChange={(e) => onInputChange('email', e.target.value)}
          placeholder={t('fields.email.placeholder')}
          required
          aria-required='true'
          className={`w-full ${validationErrors.email ? 'border-red-500' : ''}`}
        />
        {validationErrors.email && (
          <p className='text-sm text-red-600 dark:text-red-400'>
            {validationErrors.email}
          </p>
        )}
      </div>

      {/* 可选项：沟通事项 */}
      <div className='space-y-2'>
        <Label htmlFor='purpose' className='text-sm font-medium'>
          {t('fields.purpose.label')}
        </Label>
        <Select
          value={formData.purpose ?? ''}
          onValueChange={(value) => onInputChange('purpose', value)}
        >
          <SelectTrigger className='w-full'>
            <SelectValue placeholder={t('fields.purpose.placeholder')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='purchase'>
              {t('fields.purpose.options.purchase')}
            </SelectItem>
            <SelectItem value='partnership'>
              {t('fields.purpose.options.partnership')}
            </SelectItem>
            <SelectItem value='other'>
              {t('fields.purpose.options.other')}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 可选项：地区 */}
      <div className='space-y-2'>
        <Label htmlFor='region' className='text-sm font-medium'>
          {t('fields.region.label')}
        </Label>
        <Input
          id='region'
          type='text'
          value={formData.region}
          onChange={(e) => onInputChange('region', e.target.value)}
          placeholder={t('fields.region.placeholder')}
          className='w-full'
        />
      </div>

      {/* 可选项：附加信息 */}
      <div className='space-y-2'>
        <Label htmlFor='message' className='text-sm font-medium'>
          {t('fields.message.label')}
        </Label>
        <Textarea
          id='message'
          value={formData.message}
          onChange={(e) => onInputChange('message', e.target.value)}
          placeholder={t('fields.message.placeholder')}
          rows={4}
          className='w-full resize-none'
        />
      </div>
    </>
  );
}

/**
 * 联系表单组件
 *
 * 特性：
 * - 低摩擦度设计：仅2个必填项（姓名、邮箱）
 * - 可选项：沟通事项、地区、附加信息
 * - 多语言支持：支持中英文切换
 * - 响应式设计：适配移动端和桌面端
 * - 现代化UI：使用 shadcn/ui 组件
 * - 基础状态管理：表单状态和提交状态
 * - 用户体验优化：清晰的标签和占位符
 */
export function ContactForm(): ReactElement {
  const t = useTranslations('pages.contact.form');

  // 表单状态管理
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    purpose: '',
    region: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');

  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    email?: string;
  }>({});

  /**
   * 处理输入字段变化
   */
  const handleInputChange = (
    field: keyof ContactFormData,
    value: string
  ): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // 清除相关字段的验证错误
    if (validationErrors[field as keyof typeof validationErrors]) {
      setValidationErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  /**
   * 验证表单数据
   */
  const validateForm = (): boolean => {
    const errors: { name?: string; email?: string } = {};

    // 验证姓名
    if (formData.name.trim() === '') {
      errors.name = t('fields.name.required');
    }

    // 验证邮箱
    if (formData.email.trim() === '') {
      errors.email = t('fields.email.required');
    } else if (!EMAIL_REGEX.test(formData.email.trim())) {
      errors.email = t('fields.email.invalid');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent): void => {
    e.preventDefault();

    // 验证表单
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    // TODO: 在后续任务中集成实际的表单提交逻辑
    // 模拟提交延迟
    setTimeout(() => {
      // 模拟成功提交
      setSubmitStatus('success');

      // 重置表单
      setFormData({
        name: '',
        email: '',
        purpose: '',
        region: '',
        message: '',
      });

      // 清除验证错误
      setValidationErrors({});

      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div className='mx-auto max-w-2xl'>
      <Card>
        <CardHeader className='text-center'>
          <CardTitle className='text-2xl font-bold'>{t('title')}</CardTitle>
          <CardDescription className='text-lg'>{t('subtitle')}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <FormFields
              formData={formData}
              onInputChange={handleInputChange}
              t={(key: string) => t(key)}
              validationErrors={validationErrors}
            />

            {/* 提交按钮 */}
            <div className='pt-4'>
              <Button
                type='submit'
                disabled={
                  isSubmitting ||
                  formData.name.trim() === '' ||
                  formData.email.trim() === ''
                }
                className='w-full'
                size='lg'
              >
                {isSubmitting ? t('submitting') : t('submit')}
              </Button>
            </div>

            {/* 状态消息 */}
            {submitStatus === 'success' && (
              <div className='rounded-md bg-green-50 p-4 text-center text-sm text-green-800 dark:bg-green-900/20 dark:text-green-200'>
                {t('success')}
              </div>
            )}

            {submitStatus === 'error' && (
              <div className='rounded-md bg-red-50 p-4 text-center text-sm text-red-800 dark:bg-red-900/20 dark:text-red-200'>
                {t('error')}
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
