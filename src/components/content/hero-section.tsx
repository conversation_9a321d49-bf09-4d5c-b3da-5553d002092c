'use client';

import { motion } from 'framer-motion';
import { ArrowRight, Github } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';

/**
 * 动画配置
 */
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

/**
 * 处理按钮点击事件
 */
function handleViewComponents(): void {
  const demoSection = document.getElementById('demo-section');
  demoSection?.scrollIntoView({ behavior: 'smooth' });
}

function handleViewSource(): void {
  window.open(
    'https://github.com/tucsenberg/tucsenberg-web-nextjs15',
    '_blank'
  );
}

/**
 * Hero 区域组件
 *
 * 特性：
 * - 现代化设计：渐变背景和动画效果
 * - 响应式布局：移动端和桌面端适配
 * - 多语言支持：集成 next-intl
 * - 动画效果：使用 framer-motion
 * - CTA 按钮：主要和次要操作
 */
export function HeroSection(): React.JSX.Element {
  const t = useTranslations('home.hero');

  return (
    <section className='from-background via-background to-muted/20 relative overflow-hidden bg-gradient-to-br py-20 md:py-32'>
      {/* 背景装饰 */}
      <div className='bg-grid-white/[0.02] absolute inset-0 bg-[size:50px_50px]' />
      <div className='via-primary/5 absolute top-0 left-0 h-full w-full bg-gradient-to-r from-transparent to-transparent' />

      <div className='relative container mx-auto px-4'>
        <motion.div
          className='mx-auto max-w-4xl text-center'
          variants={containerVariants}
          initial='hidden'
          animate='visible'
        >
          {/* 标题 */}
          <motion.h1
            className='text-foreground mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl'
            variants={itemVariants}
          >
            <span className='from-primary to-primary/60 bg-gradient-to-r bg-clip-text text-transparent'>
              {t('title')}
            </span>
          </motion.h1>

          {/* 副标题 */}
          <motion.h2
            className='text-muted-foreground mb-6 text-xl font-medium sm:text-2xl md:text-3xl'
            variants={itemVariants}
          >
            {t('subtitle')}
          </motion.h2>

          {/* 描述 */}
          <motion.p
            className='text-muted-foreground mx-auto mb-10 max-w-2xl text-lg leading-relaxed'
            variants={itemVariants}
          >
            {t('description')}
          </motion.p>

          {/* CTA 按钮 */}
          <motion.div
            className='flex flex-col gap-4 sm:flex-row sm:justify-center'
            variants={itemVariants}
          >
            <Button
              size='lg'
              onClick={handleViewComponents}
              className='group relative overflow-hidden px-8 py-3 text-base font-medium'
            >
              <span className='relative z-10 flex items-center gap-2'>
                {t('cta.primary')}
                <ArrowRight className='h-4 w-4 transition-transform group-hover:translate-x-1' />
              </span>
              <div className='from-primary to-primary/80 absolute inset-0 bg-gradient-to-r transition-transform group-hover:scale-105' />
            </Button>

            <Button
              variant='outline'
              size='lg'
              onClick={handleViewSource}
              className='group px-8 py-3 text-base font-medium'
            >
              <Github className='mr-2 h-4 w-4' />
              {t('cta.secondary')}
            </Button>
          </motion.div>

          {/* 技术标签 */}
          <motion.div
            className='mt-16 flex flex-wrap justify-center gap-3'
            variants={itemVariants}
          >
            {[
              'Next.js 15',
              'TypeScript',
              'Tailwind CSS',
              'shadcn/ui',
              'Framer Motion',
              'next-intl',
            ].map((tech) => (
              <span
                key={tech}
                className='bg-background/50 text-muted-foreground rounded-full border px-3 py-1 text-sm font-medium backdrop-blur-sm'
              >
                {tech}
              </span>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* 底部渐变 */}
      <div className='from-background absolute right-0 bottom-0 left-0 h-20 bg-gradient-to-t to-transparent' />
    </section>
  );
}
