'use client';

import { motion } from 'framer-motion';
import { Code2, ExternalLink, Palette, Rocket, Settings } from 'lucide-react';
import { useTranslations } from 'next-intl';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

/**
 * 获取技术链接
 */
function getTechUrl(techId: string): string {
  const urls: Record<string, string> = {
    nextjs: 'https://nextjs.org',
    typescript: 'https://typescriptlang.org',
    react: 'https://react.dev',
    tailwind: 'https://tailwindcss.com',
    shadcn: 'https://ui.shadcn.com',
    framer: 'https://framer.com/motion',
    intl: 'https://next-intl-docs.vercel.app',
    eslint: 'https://eslint.org',
    prettier: 'https://prettier.io',
    vercel: 'https://vercel.com',
  };
  // eslint-disable-next-line security/detect-object-injection
  return urls[techId] ?? '#';
}

/**
 * 技术分类数据
 */
const categories = [
  {
    id: 'frontend',
    icon: Code2,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
    technologies: ['nextjs', 'typescript', 'react'],
  },
  {
    id: 'styling',
    icon: Palette,
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
    technologies: ['tailwind', 'shadcn', 'framer'],
  },
  {
    id: 'tooling',
    icon: Settings,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
    technologies: ['intl', 'eslint', 'prettier'],
  },
  {
    id: 'deployment',
    icon: Rocket,
    color: 'text-orange-500',
    bgColor: 'bg-orange-500/10',
    technologies: ['vercel'],
  },
];

/**
 * 动画配置
 */
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

/**
 * 技术栈展示组件
 *
 * 特性：
 * - 分类展示：前端、样式、工具、部署四大类
 * - 卡片布局：每个技术都有详细说明
 * - 动画效果：滚动触发和悬停效果
 * - 响应式设计：网格布局适配不同屏幕
 * - 多语言支持：完整的国际化
 */
export function TechStackSection(): React.JSX.Element {
  const t = useTranslations('home.techStack');

  return (
    <section id='tech-stack' className='py-20 md:py-32'>
      <div className='container mx-auto px-4'>
        <motion.div
          className='mx-auto max-w-4xl text-center'
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-foreground mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl'>
            {t('title')}
          </h2>
          <p className='text-muted-foreground mb-16 text-lg'>{t('subtitle')}</p>
        </motion.div>

        <motion.div
          className='grid gap-8 md:grid-cols-2 lg:grid-cols-4'
          variants={containerVariants}
          initial='hidden'
          whileInView='visible'
          viewport={{ once: true }}
        >
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <motion.div key={category.id} variants={itemVariants}>
                <Card className='group hover:shadow-primary/5 h-full transition-all duration-300 hover:shadow-lg'>
                  <CardHeader className='text-center'>
                    <div
                      className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full ${category.bgColor}`}
                    >
                      <IconComponent className={`h-8 w-8 ${category.color}`} />
                    </div>
                    <CardTitle className='text-xl'>
                      {t(`categories.${category.id}.title`)}
                    </CardTitle>
                    <CardDescription>
                      {t(`categories.${category.id}.description`)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-4'>
                      {category.technologies.map((techId) => (
                        <motion.div
                          key={techId}
                          className='group/tech hover:border-primary/50 hover:bg-primary/5 cursor-pointer rounded-lg border p-4 transition-all duration-200'
                          onClick={() =>
                            window.open(getTechUrl(techId), '_blank')
                          }
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className='flex items-start justify-between'>
                            <div className='flex-1'>
                              <div className='flex items-center gap-2'>
                                <h4 className='text-foreground font-semibold'>
                                  {t(`technologies.${techId}.name`)}
                                </h4>
                                <ExternalLink className='text-muted-foreground h-3 w-3 opacity-0 transition-opacity group-hover/tech:opacity-100' />
                              </div>
                              <p className='text-muted-foreground mt-1 text-sm'>
                                {t(`technologies.${techId}.description`)}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}
