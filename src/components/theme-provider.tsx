'use client';

import type { ReactElement } from 'react';
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes';

/**
 * 主题提供者组件
 * 基于 next-themes 0.4.6 实现三模式主题切换
 * 支持 Light/Dark/System 模式和用户偏好持久化
 */
export function ThemeProvider({
  children,
  ...props
}: ThemeProviderProps): ReactElement {
  return (
    <div suppressHydrationWarning>
      <NextThemesProvider
        attribute='class'
        defaultTheme='system'
        enableSystem
        disableTransitionOnChange
        {...props}
      >
        {children}
      </NextThemesProvider>
    </div>
  );
}
