/**
 * Tailwind CSS 4.1.11 企业级全局样式
 * CSS-first 设计系统
 */

@import 'tailwindcss';

@import 'tw-animate-css';

/*---break---
 */

@custom-variant dark (&:is(.dark *));

/**
 * CSS 变量主题系统
 * 支持 Light/Dark 模式自动切换
 */
:root {
  /* 基础颜色 */

  /* 组件颜色 */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* 交互颜色 */
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);

  /* 状态颜色 */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: 210 40% 98%;

  /* 边框和输入 */
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);

  /* 品牌色彩 */
  --brand-50: 210 40% 98%;
  --brand-100: 210 40% 96%;
  --brand-200: 214.3 31.8% 91.4%;
  --brand-300: 213 27.6% 84.1%;
  --brand-400: 215.4 16.3% 46.9%;
  --brand-500: 222.2 47.4% 11.2%;
  --brand-600: 222.2 84% 4.9%;
  --brand-700: 222.2 84% 4.9%;
  --brand-800: 222.2 84% 4.9%;
  --brand-900: 222.2 84% 4.9%;
  --brand-950: 222.2 84% 4.9%;

  /* 圆角系统 */
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

/**
 * 暗色模式 CSS 变量
 */
.dark {
  /* 基础颜色 */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);

  /* 组件颜色 */
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);

  /* 交互颜色 */
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);

  /* 状态颜色 */
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: 210 40% 98%;

  /* 边框和输入 */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);

  /* 品牌色彩（暗色模式） */
  --brand-50: 222.2 84% 4.9%;
  --brand-100: 217.2 32.6% 17.5%;
  --brand-200: 215 20.2% 65.1%;
  --brand-300: 213 27.6% 84.1%;
  --brand-400: 215.4 16.3% 46.9%;
  --brand-500: 210 40% 98%;
  --brand-600: 210 40% 98%;
  --brand-700: 210 40% 98%;
  --brand-800: 210 40% 98%;
  --brand-900: 210 40% 98%;
  --brand-950: 210 40% 98%;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/**
 * CSS-first 主题配置
 * 将 CSS 变量映射到 Tailwind 主题系统
 */
@theme {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  /* 字体系统 */
  --font-sans: var(--font-geist-sans), system-ui, sans-serif;
  --font-mono:
    var(--font-geist-mono), 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono',
    Consolas, 'Courier New', monospace;
  --font-heading: var(--font-geist-sans), system-ui, sans-serif;

  /* 圆角系统 */
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
}

/**
 * 全局样式重置和基础样式
 */
* {
  border-color: hsl(var(--border));
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/**
 * Typography 系统样式
 * 企业级排版配置
 */
.prose {
  --tw-prose-body: hsl(var(--foreground));
  --tw-prose-headings: hsl(var(--foreground));
  --tw-prose-lead: hsl(var(--muted-foreground));
  --tw-prose-links: hsl(var(--primary));
  --tw-prose-bold: hsl(var(--foreground));
  --tw-prose-counters: hsl(var(--muted-foreground));
  --tw-prose-bullets: hsl(var(--muted-foreground));
  --tw-prose-hr: hsl(var(--border));
  --tw-prose-quotes: hsl(var(--foreground));
  --tw-prose-quote-borders: hsl(var(--border));
  --tw-prose-captions: hsl(var(--muted-foreground));
  --tw-prose-code: hsl(var(--foreground));
  --tw-prose-pre-code: hsl(var(--muted-foreground));
  --tw-prose-pre-bg: hsl(var(--muted));
  --tw-prose-th-borders: hsl(var(--border));
  --tw-prose-td-borders: hsl(var(--border));
}

.dark .prose {
  --tw-prose-invert-body: hsl(var(--foreground));
  --tw-prose-invert-headings: hsl(var(--foreground));
  --tw-prose-invert-lead: hsl(var(--muted-foreground));
  --tw-prose-invert-links: hsl(var(--primary));
  --tw-prose-invert-bold: hsl(var(--foreground));
  --tw-prose-invert-counters: hsl(var(--muted-foreground));
  --tw-prose-invert-bullets: hsl(var(--muted-foreground));
  --tw-prose-invert-hr: hsl(var(--border));
  --tw-prose-invert-quotes: hsl(var(--foreground));
  --tw-prose-invert-quote-borders: hsl(var(--border));
  --tw-prose-invert-captions: hsl(var(--muted-foreground));
  --tw-prose-invert-code: hsl(var(--foreground));
  --tw-prose-invert-pre-code: hsl(var(--muted-foreground));
  --tw-prose-invert-pre-bg: hsl(var(--muted));
  --tw-prose-invert-th-borders: hsl(var(--border));
  --tw-prose-invert-td-borders: hsl(var(--border));
}

/*---break---
 */

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/*---break---
 */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
