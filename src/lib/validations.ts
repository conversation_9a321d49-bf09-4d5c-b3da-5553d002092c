import { z } from 'zod';

/**
 * 通用验证模式定义
 * 
 * 提供企业级的表单验证规则，支持多语言错误消息和类型安全。
 * 基于 Zod 3.25.67 构建，确保运行时类型安全和编译时类型推断。
 */

/**
 * 联系表单验证模式
 * 
 * 验证规则：
 * - 姓名：必填，2-50字符，支持中英文
 * - 邮箱：必填，标准邮箱格式验证
 * - 沟通事项：可选，最多100字符
 * - 地区：可选，预定义选项
 * - 附加信息：可选，最多1000字符
 */
export const contactFormSchema = z.object({
  name: z
    .string()
    .min(1, '姓名不能为空')
    .min(2, '姓名至少需要2个字符')
    .max(50, '姓名不能超过50个字符')
    .regex(
      /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
      '姓名只能包含中文、英文字母和空格'
    ),
  
  email: z
    .string()
    .min(1, '邮箱不能为空')
    .email('请输入有效的邮箱地址')
    .max(100, '邮箱地址不能超过100个字符'),
  
  purpose: z
    .string()
    .max(100, '沟通事项不能超过100个字符')
    .optional(),
  
  region: z
    .string()
    .max(50, '地区选择无效')
    .optional(),
  
  message: z
    .string()
    .max(1000, '附加信息不能超过1000个字符')
    .optional(),
});

/**
 * 联系表单数据类型
 */
export type ContactFormData = z.infer<typeof contactFormSchema>;

/**
 * 通用字符串验证
 */
export const stringValidation = {
  required: (fieldName: string) => 
    z.string().min(1, `${fieldName}不能为空`),
  
  optional: (maxLength?: number) => 
    z.string().max(maxLength || 255, `内容不能超过${maxLength || 255}个字符`).optional(),
  
  email: z
    .string()
    .min(1, '邮箱不能为空')
    .email('请输入有效的邮箱地址'),
  
  phone: z
    .string()
    .regex(
      /^1[3-9]\d{9}$|^(\+86)?1[3-9]\d{9}$/,
      '请输入有效的手机号码'
    )
    .optional(),
  
  url: z
    .string()
    .url('请输入有效的网址')
    .optional(),
};

/**
 * 数字验证
 */
export const numberValidation = {
  positive: (fieldName: string) =>
    z.number().positive(`${fieldName}必须为正数`),
  
  range: (min: number, max: number, fieldName: string) =>
    z.number()
      .min(min, `${fieldName}不能小于${min}`)
      .max(max, `${fieldName}不能大于${max}`),
  
  integer: (fieldName: string) =>
    z.number().int(`${fieldName}必须为整数`),
};

/**
 * 日期验证
 */
export const dateValidation = {
  future: (fieldName: string) =>
    z.date().refine(
      (date) => date > new Date(),
      `${fieldName}必须是未来的日期`
    ),
  
  past: (fieldName: string) =>
    z.date().refine(
      (date) => date < new Date(),
      `${fieldName}必须是过去的日期`
    ),
  
  range: (start: Date, end: Date, fieldName: string) =>
    z.date().refine(
      (date) => date >= start && date <= end,
      `${fieldName}必须在指定日期范围内`
    ),
};

/**
 * 文件验证
 */
export const fileValidation = {
  image: (maxSize: number = 5 * 1024 * 1024) => // 默认5MB
    z
      .instanceof(File)
      .refine(
        (file) => file.size <= maxSize,
        `文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB`
      )
      .refine(
        (file) => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
        '只支持 JPEG、PNG、WebP 格式的图片'
      ),
  
  document: (maxSize: number = 10 * 1024 * 1024) => // 默认10MB
    z
      .instanceof(File)
      .refine(
        (file) => file.size <= maxSize,
        `文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB`
      )
      .refine(
        (file) => [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ].includes(file.type),
        '只支持 PDF、DOC、DOCX 格式的文档'
      ),
};

/**
 * 密码验证
 */
export const passwordValidation = {
  strong: z
    .string()
    .min(8, '密码至少需要8个字符')
    .max(128, '密码不能超过128个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      '密码必须包含大小写字母、数字和特殊字符'
    ),
  
  medium: z
    .string()
    .min(6, '密码至少需要6个字符')
    .max(128, '密码不能超过128个字符')
    .regex(
      /^(?=.*[a-zA-Z])(?=.*\d)/,
      '密码必须包含字母和数字'
    ),
  
  confirm: (passwordField: string) =>
    z.string().refine(
      (value, ctx) => {
        const password = ctx.parent[passwordField as keyof typeof ctx.parent];
        return value === password;
      },
      '两次输入的密码不一致'
    ),
};

/**
 * 数组验证
 */
export const arrayValidation = {
  nonEmpty: <T>(schema: z.ZodType<T>, fieldName: string) =>
    z.array(schema).min(1, `请至少选择一个${fieldName}`),
  
  maxLength: <T>(schema: z.ZodType<T>, max: number, fieldName: string) =>
    z.array(schema).max(max, `${fieldName}最多选择${max}个`),
  
  unique: <T>(schema: z.ZodType<T>, fieldName: string) =>
    z.array(schema).refine(
      (items) => new Set(items).size === items.length,
      `${fieldName}不能有重复项`
    ),
};

/**
 * 条件验证
 */
export const conditionalValidation = {
  requiredIf: <T>(
    schema: z.ZodType<T>,
    condition: (data: any) => boolean,
    fieldName: string
  ) =>
    z.union([
      schema,
      z.undefined(),
    ]).refine(
      (value, ctx) => {
        if (condition(ctx.parent)) {
          return value !== undefined;
        }
        return true;
      },
      `当满足条件时，${fieldName}为必填项`
    ),
};
