import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 验证输入类型是否有效
 */
function isValidClassInput(input: unknown): input is ClassValue {
  return (
    input === null ||
    input === undefined ||
    typeof input === 'string' ||
    typeof input === 'number' ||
    typeof input === 'boolean' ||
    Array.isArray(input) ||
    (typeof input === 'object' && input !== null)
  );
}

/**
 * 过滤有效的类名输入
 */
function filterValidInputs(inputs: ClassValue[]): ClassValue[] {
  const validInputs: ClassValue[] = [];

  for (const input of inputs) {
    if (isValidClassInput(input)) {
      validInputs.push(input);
    } else if (process.env['NODE_ENV'] === 'development') {
      // 开发环境警告（生产环境静默）
      // eslint-disable-next-line no-console
      console.warn(
        `cn: Invalid input type "${typeof input}" detected. Skipping.`,
        input
      );
    }
  }

  return validInputs.filter((input) => {
    return input !== null && input !== undefined && input !== '';
  });
}

/**
 * 安全地合并类名
 */
function safeMergeClasses(inputs: ClassValue[]): string {
  try {
    const result = twMerge(clsx(inputs));
    return typeof result === 'string' ? result : '';
  } catch (error) {
    if (process.env['NODE_ENV'] === 'development') {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      // eslint-disable-next-line no-console
      console.error('cn function error:', { error: errorMessage, inputs });
    }
    return '';
  }
}

/**
 * 合并和去重 Tailwind CSS 类名
 *
 * 这个函数结合了 clsx 和 tailwind-merge 的功能：
 * - clsx: 处理条件类名和各种输入格式
 * - tailwind-merge: 智能合并冲突的 Tailwind 类名
 *
 * @param inputs - 类名输入，支持字符串、对象、数组等格式
 * @returns 合并后的类名字符串，去重并解决冲突
 *
 * @example
 * // 基础用法
 * cn('px-4 py-2', 'bg-blue-500', { 'text-white': true })
 * // => 'px-4 py-2 bg-blue-500 text-white'
 *
 * @example
 * // 冲突解决
 * cn('px-4 px-6', 'bg-red-500 bg-blue-500')
 * // => 'px-6 bg-blue-500'
 *
 * @example
 * // 条件类名
 * cn('base-class', { 'active-class': isActive, 'disabled-class': isDisabled })
 */
export function cn(...inputs: ClassValue[]): string {
  if (inputs.length === 0) return '';

  const filteredInputs = filterValidInputs(inputs);
  if (filteredInputs.length === 0) return '';

  return safeMergeClasses(filteredInputs);
}

/**
 * 安全的环境变量访问工具
 *
 * 提供类型安全和安全的环境变量访问，防止信息泄露和注入攻击
 * 符合 CWE-200 (信息暴露) 安全标准
 */

// 允许访问的环境变量白名单（防止敏感信息泄露）
const ALLOWED_ENV_VARS = new Set([
  'NODE_ENV',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_SITE_NAME',
  'NEXT_PUBLIC_SITE_DESCRIPTION',
  'NEXT_PUBLIC_DEFAULT_LOCALE',
  'NEXT_PUBLIC_SUPPORTED_LOCALES',
  // 添加其他允许的公共环境变量
]);

/**
 * 验证环境变量键名
 */
function validateEnvKey(key: string): string {
  if (typeof key !== 'string') {
    throw new Error(`getEnvVar: Key must be a string, received ${typeof key}`);
  }

  const trimmedKey = key.trim();
  if (trimmedKey === '') {
    throw new Error('getEnvVar: Key cannot be empty');
  }

  if (!/^\w+$/i.test(trimmedKey)) {
    throw new Error(
      `getEnvVar: Invalid key format "${trimmedKey}". Only alphanumeric characters and underscores are allowed.`
    );
  }

  return trimmedKey;
}

/**
 * 检查环境变量访问权限
 */
function checkEnvAccess(key: string): void {
  if (!ALLOWED_ENV_VARS.has(key)) {
    if (process.env['NODE_ENV'] === 'development') {
      // eslint-disable-next-line no-console
      console.warn(
        `getEnvVar: Access denied for "${key}". Variable not in whitelist.`
      );
    }
    throw new Error(
      `getEnvVar: Access denied for environment variable "${key}"`
    );
  }
}

/**
 * 获取环境变量的安全方法
 *
 * 安全特性：
 * - 白名单验证：只允许访问预定义的安全环境变量
 * - 类型验证：确保键名和默认值的类型安全
 * - 注入防护：防止通过键名进行代码注入
 * - 信息泄露防护：避免敏感环境变量被意外访问
 *
 * @param key - 环境变量键名（必须在白名单中）
 * @param defaultValue - 默认值
 * @returns 环境变量值或默认值
 * @throws {Error} 当尝试访问不在白名单中的环境变量时
 *
 * @example
 * // 安全访问
 * const nodeEnv = getEnvVar('NODE_ENV', 'development');
 *
 * @example
 * // 公共配置访问
 * const appUrl = getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000');
 */
export function getEnvVar(key: string, defaultValue = ''): string {
  if (typeof defaultValue !== 'string') {
    throw new Error(
      `getEnvVar: Default value must be a string, received ${typeof defaultValue}`
    );
  }

  const validKey = validateEnvKey(key);
  checkEnvAccess(validKey);

  try {
    // eslint-disable-next-line security/detect-object-injection
    const value = process.env[validKey];

    if (value !== undefined && typeof value !== 'string') {
      throw new Error(
        `getEnvVar: Environment variable "${validKey}" is not a string`
      );
    }

    return value ?? defaultValue;
  } catch (error) {
    if (process.env['NODE_ENV'] === 'development') {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      // eslint-disable-next-line no-console
      console.error(`getEnvVar error for key "${validKey}":`, errorMessage);
    }
    return defaultValue;
  }
}

/**
 * 检查环境变量是否在白名单中
 *
 * @param key - 环境变量键名
 * @returns 是否允许访问
 */
export function isEnvVarAllowed(key: string): boolean {
  if (typeof key !== 'string') {
    return false;
  }

  const trimmedKey = key.trim();
  return ALLOWED_ENV_VARS.has(trimmedKey);
}

/**
 * 获取所有允许的环境变量列表
 *
 * @returns 允许访问的环境变量键名数组
 */
export function getAllowedEnvVars(): readonly string[] {
  return [...ALLOWED_ENV_VARS];
}
