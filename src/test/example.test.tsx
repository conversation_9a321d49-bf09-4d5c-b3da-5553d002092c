import { describe, expect, it } from 'vitest';
import { render, screen } from './utils';

// 简单的测试组件
const TestComponent = ({
  message,
}: {
  message: string;
}): React.ReactElement => <div data-testid='test-message'>{message}</div>;

describe('测试框架配置验证', () => {
  it('应该能够渲染 React 组件', () => {
    const testMessage = 'Hello, Vitest!';
    render(<TestComponent message={testMessage} />);

    const messageElement = screen.getByTestId('test-message');
    expect(messageElement).toBeInTheDocument();
    expect(messageElement).toHaveTextContent(testMessage);
  });

  it('应该支持基本的断言', () => {
    expect(1 + 1).toBe(2);
    expect('hello').toMatch(/hello/);
    expect([1, 2, 3]).toHaveLength(3);
  });

  it('应该支持异步测试', async () => {
    const promise = Promise.resolve('async result');
    await expect(promise).resolves.toBe('async result');
  });

  it('应该支持 DOM 查询', () => {
    render(
      <div>
        <button>Click me</button>
        <input placeholder='Enter text' />
      </div>
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter text')).toBeInTheDocument();
  });
});
